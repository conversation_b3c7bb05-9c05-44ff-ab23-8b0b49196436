<!--pages/featured-properties/featured-properties.wxml-->
<view class="container">

  <!-- 页面头部图片 -->
  <view class="header-section">
    <image src="/image/banner.png" class="header-background"></image>
  </view>

  <!-- 区域筛选按钮 -->
  <view class="area-filter-section">
    <view class="area-filter-container">
      <view class="area-filter-btn {{selectedAreaIndex === index ? 'active' : ''}}"
            wx:for="{{areaOptions}}"
            wx:key="index"
            bindtap="onAreaFilterTap"
            data-index="{{index}}">
        <text class="area-filter-text">{{item}}</text>
      </view>
    </view>
  </view>
  <!-- 房源列表 -->
  <view class="property-list">
    <view class="property-card"
          wx:for="{{currentPropertyList}}"
          wx:key="id"
          bindtap="onPropertyTap"
          data-id="{{item.id}}">

      <!-- 房源信息 -->
      <view class="property-info">
        <!-- 标题和时间 -->
        <view class="title-row">
          <text class="property-title">{{item.title}} {{item.subtitle}}</text>
          <view class="price-section">
          <view class="price-main">
            <text class="total-price">{{item.totalPrice}}</text>
            <text class="unit-price">{{item.unitPrice}}</text>
          </view>
        </view>
        </view>

        <!-- 房源详情 -->
        <view class="property-details">
          <text class="detail-item">{{item.area}}</text>
          <text class="detail-separator">|</text>
          <text class="detail-item">{{item.direction}}</text>
          <text class="detail-separator">|</text>
          <text class="detail-item">{{item.floor}}</text>
          <text class="detail-separator">|</text>
          <text class="detail-item">{{item.location}}</text>
         
        </view>

        <!-- 价格信息 -->
      

        <!-- 必卖理由 - 重点突出 -->
        <view class="must-sell-reason">
          <view class="reason-header">
            <text class="reason-icon">⚡</text>
            <text class="reason-title">必卖理由</text>
          </view>
          <view class="reason-content">
            <text class="reason-text">{{item.mustSellReason}}</text>
          </view>
        </view>

        <!-- 房源标签 -->
        <view class="property-tags" wx:if="{{item.tags && item.tags.length > 0}}">
          <view class="property-tag" wx:for="{{item.tags}}" wx:key="index">{{item}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-section" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 没有数据提示 -->
  <view class="no-data-tip" wx:if="{{currentPropertyList.length === 0 && !loading}}">
    <text class="tip-text">暂无{{areaOptions[selectedAreaIndex]}}房源信息</text>
  </view>

</view>