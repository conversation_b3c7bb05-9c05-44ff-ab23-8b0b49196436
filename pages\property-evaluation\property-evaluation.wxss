/* pages/property-evaluation/property-evaluation.wxss */
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 10rpx;
  background-color: #f5f5f5;
  box-sizing: border-box;
  overflow-y: auto;
  padding-bottom: 120rpx;
}

/* 标签页切换 */
.tab-container {
  width: 100%;
  height: 100rpx;
  background-color: white;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
  margin: 0 -20rpx 20rpx -20rpx;
  position: relative;
  z-index: 10;
}

.tab-item {
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #FF4444;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 6rpx;
  background-color: #FF4444;
  border-radius: 3rpx;
}

.tab-text {
  font-size: 30rpx;
  font-weight: 500;
  color: inherit;
}

/* 内容区域 */
.content-container {
  width: 100%;
  position: relative;
}

.content-panel {
  width: 100%;
  transition: opacity 0.3s ease;
}

.content-panel.show {
  display: block;
  opacity: 1;
}

.content-panel.hide {
  display: none;
  opacity: 0;
}

/* 表单容器 */
.form-container {
  padding: 30rpx;
  width: 100%;
}

/* 表单区块 */
.form-section {
  background-color: white;
  overflow: hidden;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.section-title {
  background-color: #FFFFFF;
  font-size: 32rpx;
  color: #333;
  padding: 30rpx 20rpx;
  font-weight: 600;
}

/* 提示文字 */
.submit-record {
  background-color: #fff3e0;
  border-left: 4rpx solid #ff9800;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 8rpx;
}

.record-text {
  font-size: 28rpx;
  color: #e65100;
  line-height: 1.5;
}

/* 表单项 */
.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 20rpx;
  min-height: 80rpx;
  border-bottom: 1rpx solid rgb(204, 199, 199);
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  min-width: 160rpx;
}

/* 输入框容器 */
.form-input-container {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.form-input {
  flex: 1;
  text-align: right;
  font-size: 32rpx;
  color: #333;
  padding: 10rpx 0;
  max-width: 400rpx;
}

.form-input::placeholder {
  color: #999;
  font-size: 30rpx;
}

/* 选择器值容器 */
.form-value-container {
  display: flex;
  align-items: center;
}

.form-value {
  font-size: 32rpx;
  color: #666;
  margin-right: 10rpx;
}

.arrow-text {
  font-size: 28rpx;
  color: #999;
  margin-left: 10rpx;
}

/* 单选按钮组 */
.radio-group {
  display: flex;
  gap: 40rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: 10rpx;
  cursor: pointer;
}

.radio-circle {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.radio-item.selected .radio-circle {
  border-color: #FF4444;
}

.radio-dot {
  width: 20rpx;
  height: 20rpx;
  background-color: #FF4444;
  border-radius: 50%;
}

.radio-text {
  font-size: 30rpx;
  color: #333;
}

/* 文本域 */
.textarea-item {
  flex-direction: column;
  align-items: flex-start;
}

.form-textarea {
  width: 100%;
  min-height: 120rpx;
  font-size: 30rpx;
  color: #333;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-top: 20rpx;
  box-sizing: border-box;
}

.form-textarea::placeholder {
  color: #999;
}

/* 提交按钮 */
.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background-color: #FF4444;
  color: white;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 12rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-btn:active {
  background-color: #e63939;
  transform: scale(0.98);
}

/* 响应式设计 */
@media (max-width: 400px) {
  .tab-text {
    font-size: 26rpx;
  }
  
  .form-label {
    font-size: 28rpx;
    min-width: 140rpx;
  }
  
  .form-input {
    font-size: 28rpx;
  }
}

/* 动画效果 */
.tab-item:active {
  background-color: #f8f8f8;
}

.form-item:active {
  background-color: #f8f8f8;
}

/* 表单验证提示 */
.form-error {
  border-color: #ff4444 !important;
}

.error-text {
  color: #ff4444;
  font-size: 24rpx;
  margin-top: 10rpx;
}
