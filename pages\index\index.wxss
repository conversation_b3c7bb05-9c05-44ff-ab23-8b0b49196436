/**index.wxss**/
.container {
  width: 750rpx;
  background-color: #f5f5f5;
  margin-top: -220rpx;
  height: 1500rpx;
}

/* 顶部红色区域 */
.header {
  width: 750rpx;
  height: 900rpx;
  background:#c00000;
  position: relative;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.battery-icon {
  width: 48rpx;
  height: 24rpx;
  border: 2rpx solid white;
  border-radius: 4rpx;
  position: relative;
}

.battery-icon::after {
  content: '';
  position: absolute;
  right: -6rpx;
  top: 6rpx;
  width: 4rpx;
  height: 12rpx;
  background-color: white;
  border-radius: 0 2rpx 2rpx 0;
}

.notch {
  width: 300rpx;
  height: 60rpx;
  background-color: black;
  border-radius: 0 0 30rpx 30rpx;
  margin: 0 auto;
  position: relative;
  top: -20rpx;
}

.header-content {
  display: flex;
  align-items: center;
  padding: 40rpx 60rpx;
  position: relative;
}

.user-avatar {
  position: relative;
  margin-right: 40rpx;
}

.avatar-dots {
  display: flex;
  gap: 8rpx;
  margin-bottom: 20rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background-color: white;
  border-radius: 50%;
}

.avatar-circle {
  width: 80rpx;
  height: 80rpx;
  background-color: white;
  border-radius: 50%;
  position: relative;
}

.avatar-circle::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60rpx;
  height: 60rpx;
  background-color: #FF4444;
  border-radius: 50%;
}

.title-section {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 337rpx;
  position: relative;
  left: 70rpx;
}

.title-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  width: auto;
}

.main-title {
  display: block;
  font-size: 90rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  letter-spacing: 4rpx;
  text-align: left;
  width: 100%;
}

.subtitle {
  display: block;
  font-size: 50rpx;
  opacity: 0.9;
  letter-spacing: 2rpx;
  text-align: left;
  width: 100%;
}

/* 内容区域 */
.section {
  padding: 35rpx 40rpx 40rpx;
  margin-bottom: -40rpx;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #FF4444;
  margin-bottom: 30rpx;
}

/* 工具网格 */
.tool-grid {
  display: flex;
  justify-content: space-between;
  gap: 10rpx;
  width: 600rpx;
}



.tool-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 20rpx;
  background-color: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.tool-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.icon-image {
  width: 48rpx;
  height: 48rpx;
}
.service-item{
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 20rpx;
  background-color: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}






.tool-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 服务网格 */
.service-grid {
  display: flex;
  justify-content:center;
  gap: 10rpx;
  width: 600rpx;
}
.service-g {
  display: flex;
  justify-content:center;
  gap: 10rpx;
  width: 600rpx;
  margin-top:20rpx ;
}
.gsjieshao{
  display: flex;
  justify-content:center;
  gap: 10rpx;
  width: 570rpx;
  margin-top:20rpx;
  padding: 20rpx;
  background: rgb(248, 122, 122);
  color: white;
  border-radius:20rpx ;
}
.tool-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 20rpx;
  background-color: white;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  position: relative;
}

.tool-item.highlighted {
  border: 4rpx solid #FF4444;
}

.service-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}

.tool-item.highlighted .service-icon {
  background-color: #FF4444;
}

.service-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.tool-item.highlighted .service-text {
  color: #FF4444;
  font-weight: 600;
}
