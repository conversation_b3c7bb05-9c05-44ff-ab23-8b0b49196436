<!--pages/house-consultation/house-consultation.wxml-->
<view class="container">
  <!-- 购房咨询卡片 -->
  <image class="lop" src="/image/xingzhouzixun.png" mode="widthFix"></image>
  <!-- 价格信息 -->
  <view class="price-section">
    <view class="price-info">
      <text class="service-type">{{serviceInfo.serviceType}}</text>
      <view class="price-container">
        <text class="price-symbol">¥</text>
        <text class="price-amount" >{{serviceInfo.price}}</text>
      </view>
      <text class="buy-count" >{{serviceInfo.buyCount}}</text>
    </view>
  </view>

  <!-- 销售导语 -->
  <view class="sales-description">
  <!-- 长图替代文字内容 -->
  <view class="description-image-container">
    <image 
      class="description-long-img" 
      src="/image/xingzhouzixun.png"
      mode="widthFix"
      lazy-load
      alt="买房咨询服务介绍"
    ></image>
  </view>
</view>

  <!-- 购买区域 -->
  <view class="purchase-section">
   
    <!-- 购买按钮 -->
    <view class="purchase-button" bindtap="onPurchase">
      立即购买
    </view>
  </view>

  <!-- 第一步：购买弹窗 -->
  <view class="modal-overlay" wx:if="{{showPurchaseModal}}" bindtap="closePurchaseModal">
    <view class="purchase-modal" catchtap="">
      <!-- 拖拽指示器 -->
      <view class="modal-handle"></view>
      <!-- 关闭按钮 -->
      <view class="modal-close" bindtap="closePurchaseModal">×</view>

      <view class="modal-content">
        <!-- 左侧图片区域 -->
        <view class="modal-left">
          <image src="/image/xingzhouzixun.png" mode="aspectFill"></image>
        </view>

        <!-- 右侧内容区域 -->
        <view class="modal-right">
          <!-- 价格显示 -->
          <view class="modal-price">
            <text class="price-symbol">¥</text>
            <text class="price-amount">498.00</text>
          </view>

     <view class="lllo">
          <!-- 买房顾问 -->
          <view class="modal-section">
            <text class="section-title">买房顾问</text>
            <view class="section-tag">行舟</view>
          </view>

          <!-- 咨询服务 -->
          <view class="modal-section">
            <text class="section-title">咨询服务</text>
            <view class="section-tag">房产深度咨询</view>
          </view>

          <!-- 服务期限 -->
          <view class="modal-section">
            <text class="section-title">服务期限</text>
            <view class="section-tag">长期服务，至买到房为止</view>
          </view>
     </view>
        </view>
      </view>

      <!-- 立即购买按钮 -->
      <view class="modal-purchase-btn" bindtap="confirmPurchase">
        立即购买
      </view>
    </view>
  </view>

  <!-- 第二步：支付确认弹窗 -->
  <view class="modal-overlay" wx:if="{{showPaymentModal}}" bindtap="closePaymentModal">
    <view class="payment-modal" catchtap="">
      <view class="modal-handle"></view>
      <view class="modal-close" bindtap="closePaymentModal">×</view>

      <view class="payment-content">
        <view class="payment-title">支付确认</view>
        <view class="payment-amount">
          <text>支付金额：</text>
          <text class="amount">¥{{purchaseInfo.price}}</text>
        </view>
        <view class="payment-service">房产深度咨询服务</view>

        <view class="payment-buttons">
          <view class="cancel-btn" bindtap="closePaymentModal">取消</view>
          <view class="confirm-btn" bindtap="confirmPayment">确认支付</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 第三步：二维码弹窗 -->
  <view class="modal-overlay" wx:if="{{showQrcodeModal}}" bindtap="closeQrcodeModal">
    <view class="qrcode-modal" catchtap="">
      <view class="modal-handle"></view>
      <view class="modal-close" bindtap="closeQrcodeModal">×</view>

      <view class="qrcode-content">
        <view class="qrcode-title">购买成功</view>
        <view class="qrcode-subtitle">请扫描二维码添加微信群</view>

        <view class="qrcode-image">
          <image src="/image/maifang.png" mode="aspectFit"></image>
        </view>

        <view class="qrcode-tips">
          <text>扫描二维码加入专属咨询群</text>
          <text>专业顾问将为您提供一对一服务</text>
        </view>

        <view class="qrcode-close-btn" bindtap="closeQrcodeModal">
          我知道了
        </view>
      </view>
    </view>
  </view>

</view>
