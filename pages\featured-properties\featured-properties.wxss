/* pages/featured-properties/featured-properties.wxss */

.container {
  background: #f5f5f5;
  padding: 30rpx 10rpx;
}

/* 页面头部样式 */
.header-section {
  position: relative;
  width: 100%;
  height: 450rpx;
  margin: -30rpx -20rpx 30rpx -20rpx;
  overflow: hidden;
}

.header-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 50%, #FFB74D 100%);
  position: relative;
}

.header-background::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.header-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.5);
  letter-spacing: 4rpx;
}

/* 区域筛选样式 */
.area-filter-section {
  margin-top: -150rpx;
  border-radius: 15rpx;
  padding: 30rpx 30rpx;
  margin-bottom: 30rpx;
  z-index: 100;
}

.area-filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15rpx;
}

.area-filter-btn {
  flex: 1;
  min-width: 120rpx;
  height: 70rpx;
  background: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.area-filter-btn.active {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  border-color: #FF4444;
  box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.3);
  transform: translateY(-2rpx);
}

.area-filter-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  transition: color 0.3s ease;
}

.area-filter-btn.active .area-filter-text {
  color: white;
  font-weight: bold;
}

/* 房源列表样式 */
.property-list {
  width: 100%;
}

.property-card {
  background: white;
  border-radius: 15rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.property-card:active {
  transform: translateY(2rpx);
  box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 房源信息样式 */
.property-info {
  width: 100%;
  padding: 20rpx 10rpx;
}

/* 标题行样式 */
.title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15rpx;
}

.property-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  padding: 0 20rpx;
}

.time-tag {
  background: #f0f0f0;
  color: #999;
  font-size: 20rpx;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  white-space: nowrap;
  position: relative;
  right:30rpx;
}

/* 房源详情样式 */
.property-details {
  display: flex;
  align-items: center;
  margin-bottom: 15rpx;
  flex-wrap: wrap;
  position: relative;
  right: -20rpx;
}

.detail-item {
  font-size: 26rpx;
  color: #666;
}

.detail-separator {
  font-size: 24rpx;
  color: #ccc;
  margin: 0 10rpx;
}

/* 价格信息样式 */
.price-section {
  margin-bottom: 20rpx;
  position: relative;
  left: 20rpx;
}

.price-main {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 5rpx;
  position: relative;
  right: 70rpx;
  top: 25rpx;
  align-items: center;
}

.total-price {
  font-size: 38rpx;
  font-weight: bold;
  color: #FF4444;
}

.unit-price {
  font-size: 26rpx;
  color: #999;
}

/* 必卖理由样式 - 重点突出 */
.must-sell-reason {
  background: #faefe2;
  border: 2rpx solid #FaefeD;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 2rpx 8rpx rgba(255, 183, 77, 0.2);
  width: 90%;
  left: 10rpx;
}

.must-sell-reason::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(135deg, #FFB74D, #FF8A65);
  border-radius: 12rpx;
  z-index: -1;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5rpx rgba(255, 183, 77, 0.5);
  }
  to {
    box-shadow: 0 0 15rpx rgba(255, 183, 77, 0.8);
  }
}

.reason-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.reason-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
  color: #FF6B35;
}

.reason-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #E65100;
  text-shadow: 1rpx 1rpx 2rpx rgba(230, 81, 0, 0.1);
}

.reason-content {
  padding-left: 36rpx;
}

.reason-text {
  font-size: 26rpx;
  color: #BF360C;
  line-height: 1.6;
  font-weight: 500;
}

/* 房源标签样式 */
.property-tags {
  display: flex;
  gap: 10rpx;
  flex-wrap: wrap;
  position: relative;
  left: 20rpx;
}

.property-tag {
  background: linear-gradient(135deg, #f05959 0%, #FF6B6B 100%);
  color: white;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  
}

/* 加载状态样式 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 40rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #FF4444;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 无数据提示样式 */
.no-data-tip {
  text-align: center;
  padding: 80rpx 40rpx;
  background: white;
  border-radius: 15rpx;
  margin-top: 20rpx;
}

.no-data-tip .tip-text {
  font-size: 28rpx;
  color: #999;
}