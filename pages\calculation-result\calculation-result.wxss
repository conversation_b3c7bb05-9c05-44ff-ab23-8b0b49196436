/* pages/calculation-result/calculation-result.wxss */
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
  width: 750rpx;
  margin-top: -200rpx;
}

/* 顶部切换标签 */
.tab-container {
  display: flex;
  background-color: white;
  width: 750rpx;
  padding: 30rpx 60rpx 0;
  box-sizing: border-box;
}

.tab-item {
  flex: 1;
  text-align: center;
  position: relative;
  padding-bottom: 30rpx;
}

.tab-text {
  font-size: 32rpx;
  color: #999;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #FF4444;
  font-weight: bold;
}

.tab-underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #FF4444;
  border-radius: 3rpx;
}

/* 计算结果卡片 */
.result-card {
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  width: 750rpx;
  margin: 20rpx 0;
  padding: 40rpx;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
  box-sizing: border-box;
}

.result-header {
  text-align: center;
  margin-bottom: 20rpx;
  position: relative;
}
.result-title{
  font-size: 30rpx;
}

/* 贷款类型容器 */
.loan-type-container {
  margin-bottom: 10rpx;
  height: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 贷款类型文字 - 大字体 */
.loan-type-text {
  font-size: 40rpx;
  font-weight: 600;
  color: white;
  margin-top: -50rpx;
}

/* 支付类型文字 - 正常大小 */
.payment-type-text {
  font-size: 30rpx;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
}




.result-amount {
  text-align: center;
  margin-bottom: 40rpx;
}

.amount-container {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.amount-number {
  font-size: 72rpx;
  font-weight: bold;
  color: white;
}

.amount-unit {
  font-size: 32rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-left: 10rpx;
}

/* 帮助图标 */
.help-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  background-color: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.help-icon:active {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(0.95);
}

.help-text {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.result-details {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.detail-item {
  flex: 1;
  text-align: center;
}

.detail-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10rpx;
}

.detail-value {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: white;
}

.detail-divider {
  width: 1rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 20rpx;
}

/* 卡片内说明文字 */
.card-notice-text {
  padding: 20rpx 0;
  text-align: center;
  margin: 20rpx 0;
  margin-top: -50rpx;
}

.card-notice-highlight {
  display: block;
  font-size: 24rpx;
  color: white;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.card-notice-content {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.card-notice-decrease {
  display: block;
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 说明文字 */
.notice-text {
  width: 750rpx;
  padding: 20rpx 30rpx;
  font-size: 26rpx;
  line-height: 1.6;
  color: #666;
  box-sizing: border-box;
  margin-bottom: 10rpx;
}

.notice-highlight {
  color: #FF4444;
  font-weight: bold;
}

.notice-content {
  color: #666;
}

.notice-decrease {
  display: block;
  font-size: 22rpx;
  color: #999;
  margin-top: 10rpx;
  line-height: 1.4;
}

/* 还款明细表格 */
.table-container {
  width: 750rpx;
  margin: 0 0 20rpx 0;
  background-color: white;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.table-header {
  display: flex;
  background-color: #f8f9fa;
  border-bottom: 1rpx solid #eee;
}

.table-cell {
  flex: 1;
  padding: 20rpx 10rpx;
  text-align: center;
  font-size: 26rpx;
  border-right: 1rpx solid #eee;
}

.table-cell:last-child {
  border-right: none;
}

.header-cell {
  font-weight: bold;
  color: #333;
}

.table-body {
  max-height: 800rpx;
  transition: opacity 0.3s ease;
  /* 优化大数据量滚动性能 */
  -webkit-overflow-scrolling: touch;
}

.table-row {
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
  opacity: 1;
  transform: translateY(0);
}

.table-row:last-child {
  border-bottom: none;
}

.table-row .table-cell {
  color: #666;
  font-size: 24rpx;
}

/* 底部按钮区域 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 10rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666;
}

.phone-consult-btn {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #FF4444 0%, #FF6B6B 100%);
  color: white;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 68, 68, 0.3);
}

.phone-consult-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(255, 68, 68, 0.3);
}

/* 响应式设计 */
@media (max-width: 375px) {
  .tab-text {
    font-size: 30rpx;
  }
  
  .amount-number {
    font-size: 64rpx;
  }
  
  .table-cell {
    font-size: 24rpx;
    padding: 15rpx 8rpx;
  }
}

/* 组合贷款详细信息 */
.combined-details {
  margin: 30rpx 30rpx 0;
}

/* 组合贷款网格布局 */
.combined-grid {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.combined-item {
  flex: 1;
  background-color: white;
  border-radius: 16rpx;
  padding: 25rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.commercial-item {
  border-left: 4rpx solid #ff1717;
}

.fund-item {
  border-left: 4rpx solid #1890ff;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.item-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.item-rate {
  font-size: 24rpx;
  color: #666;
  background-color: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.item-content {
  margin-bottom: 20rpx;
}

.amount-display {
  display: flex;
  align-items: baseline;
  margin-bottom: 15rpx;
}

.item-amount-number {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.item-amount-unit {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}

.monthly-payment {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8f9fa;
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
}

.payment-label {
  font-size: 24rpx;
  color: #666;
}

.payment-value {
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
}

.item-footer {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 15rpx;
}

.interest-info {
  font-size: 24rpx;
  color: #999;
}

/* 组合贷款汇总卡片 */
.combined-summary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  padding: 30rpx;
  color: white;
  box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}

.summary-header {
  text-align: center;
  margin-bottom: 25rpx;
}

.summary-title {
  font-size: 32rpx;
  font-weight: 600;
  color: white;
}



.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.summary-row.main-row {
  padding: 20rpx 0;
}

.summary-label {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
}

.summary-value {
  font-size: 28rpx;
  font-weight: 600;
  color: white;
}

.summary-value.highlight {
  font-size: 32rpx;
  color: #fff;
}

.summary-divider {
  height: 1rpx;
  background-color: rgba(255, 255, 255, 0.2);
  margin: 10rpx 0;
}

/* 响应式布局 */
@media (max-width: 600rpx) {
  .combined-grid {
    flex-direction: column;
  }

  .combined-item {
    margin-bottom: 20rpx;
  }
}
