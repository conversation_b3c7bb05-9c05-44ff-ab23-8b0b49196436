/**tools.wxss**/
.container {
  width: 98%;
  margin: 0 auto;
  padding: 0 20rpx;
  position: relative;
  box-sizing: border-box;
  min-height: 100vh;
}

/* 水印层 */
.watermark-layer {
  position: fixed;
  top: 0;
  left: 0rpx;
  width: 100vw;
  height: 150vh;
  z-index: -1;
  pointer-events: none;
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(10, 1fr);
  gap: 40rpx 90rpx;
  padding: 30rpx 30rpx;
  transform: rotate(-45deg);
  opacity: 1;
}

.watermark-text {
  font-size: 36rpx;
  color: #ccc;
  font-weight: 300;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  opacity: 0.8;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.icon-dots {
  display: flex;
  gap: 6rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #999;
  border-radius: 50%;
}

.icon-circle {
  width: 40rpx;
  height: 40rpx;
  background-color: #333;
  border-radius: 50%;
}

/* 搜索栏 */
.search-container {
  padding: 20rpx 0;
  position: relative;
  z-index: 2;
  margin-top: 20rpx;
  width: 100%;
}

.search-box {
  height: 60rpx;
  display: flex;
  align-items: center;
  border: 2rpx solid #999;
  border-radius: 20rpx;
  padding: 0 20rpx;
}

.search-icon {
  width: 50rpx;
  height: 50rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
}

/* 筛选标签 */
.filter-container {
  width: 100%;
  position: relative;
  z-index: 2;
  margin-bottom: 20rpx;
}

.filter-scroll {
  width: 100%;
  overflow: hidden;
}

/* 隐藏主区域筛选滚动条 */
.filter-scroll::-webkit-scrollbar {
  display: none;
}

.filter-tags {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
  width: 100%;
  padding: 0;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 40rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  white-space: nowrap;
  flex: 1;
  min-width: 60rpx;
  padding: 0 10rpx;
}

.filter-tag.active {
  background-color: #FF4444;
}

.filter-text {
  font-size: 28rpx;
  color: #333;
}

.filter-tag.active .filter-text {
  color: white;
}

/* 子区域筛选标签 */
.sub-filter-container {
  width: 100%;
  position: relative;
  z-index: 2;
  margin: 10rpx 0 20rpx 0;
}

.sub-filter-scroll {
  width: 100%;
  white-space: nowrap;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 和 Edge */
}

/* 隐藏子区域筛选滚动条 */
.sub-filter-scroll::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
}

.sub-filter-tags {
  display: flex;
  padding: 0;
  gap: 15rpx;
}

.sub-filter-tag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 36rpx;
  padding: 0 15rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  white-space: nowrap;
  flex-shrink: 0;
  border: 1rpx solid #999;
}

.sub-filter-tag.active {
  background-color: red;
  border-color: #999;
}

.sub-filter-text {
  font-size: 24rpx;
  color: #333;
}

.sub-filter-tag.active .sub-filter-text {
  color: white;
}

/* 房源列表 */
.property-list {
  width: 100%;
  position: relative;
  z-index: 2;
  padding-bottom: 40rpx;
}

.property-item {
  width: 100%;
  border-bottom:2rpx solid #999;
  padding: 20rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-sizing: border-box;
}

.property-main {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20rpx;
}

.property-left {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.property-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.qwq{
  background-color: #abb7ce;
  color: white;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  font-size: 24rpx;
  margin-right: 8rpx;
}

.qqq{
  font-size: 30rpx;
}

.price-la{
  font-size: 26rpx;
  color: #666;
  margin-left: 10rpx;
}

.property-details {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.detail-item {
  font-size: 26rpx;
  color: #666;
}

.detail-opo {
  font-size: 26rpx;
  color: #666;
}

.property-price {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.price-label {
  font-size: 26rpx;
  color: #666;
}

.price-value {
  font-size: 32rpx;
  font-weight: 600;
  color: #FF4444;
}

.property-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
  min-width: 120rpx;
}

.property-date {
  color: #999;
  white-space: nowrap;
  font-size: 26rpx;
}

.pooo{
  color: #999;
  white-space: nowrap;
  font-size: 30rpx;
  position: relative;
  left: 10rpx;
}

.area-l{
  font-size: 26rpx;
  color: #666;
  position: relative;
  right: 230rpx;
}

.property-unit {
  display: flex;
  align-items: baseline;
  gap: 6rpx;
}

.area-label{
  white-space: nowrap;
  font-size: 26rpx;
  color: #666;
  position: relative;
  right: 200rpx;
}

.unit-label {
  font-size: 26rpx;
  color: #666;
  position: relative;
  right: 250rpx;
}

.area-value,
.unit-value {
  font-size: 26rpx;
  color: #333;
}

/* 底部导航 */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 120rpx;
  background-color: white;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  z-index: 10;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.nav-icon {
  width: 48rpx;
  height: 48rpx;
}

.nav-text {
  font-size: 24rpx;
  color: #666;
}

/* 点击效果 */
.property-item:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
  transition: all 0.1s ease;
}

.filter-tag:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}

.sub-filter-tag:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}

.nav-item:active {
  background-color: #f8f8f8;
  transition: all 0.1s ease;
}
