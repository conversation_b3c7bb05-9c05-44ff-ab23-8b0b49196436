<!--property-evaluation.wxml-->
<view class="container">
  <!-- 标签页切换 -->
  <view class="tab-container">
    <view
      class="tab-item {{currentTab === 0 ? 'active' : ''}}"
      bindtap="switchTab"
      data-index="0"
    >
      <text class="tab-text">房产价值评测买房版</text>
    </view>
    <view
      class="tab-item {{currentTab === 1 ? 'active' : ''}}"
      bindtap="switchTab"
      data-index="1"
    >
      <text class="tab-text">房产价值评测卖房版</text>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-container">
    <!-- 买房版内容 -->
    <view class="content-panel {{currentTab === 0 ? 'show' : 'hide'}}">
      <!-- 表单区域 -->
      <view class="form-container">
        <!-- 房屋信息表单 -->
        <view class="form-section">
          <!-- 提示文字 -->
          <view class="submit-record">
            <text class="record-text">请根据您的实际情况填写，提供的信息越详细，评估结果越准确。</text>
          </view>
          
          <!-- 小区名称 -->
          <view class="form-item">
            <view class="form-label">小区名称</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入小区名称" value="{{buyFormData.communityName}}" data-field="communityName" data-type="buy" bindinput="onInput"/>
            </view>
          </view>

          <!-- 房产类型 -->
          <picker range="{{propertyTypeOptions}}" value="{{buyPickerIndexes.propertyType}}" data-field="propertyType" data-type="buy" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">房产类型</view>
              <view class="form-value-container">
                <text class="form-value">{{buyFormData.propertyType || '请选择房产类型'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>

          <!-- 其他房产类型输入 -->
          <view wx:if="{{buyFormData.propertyType === '其他'}}" class="form-item">
            <view class="form-label">请具体说明</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请具体说明房产类型" value="{{buyFormData.otherPropertyType}}" data-field="otherPropertyType" data-type="buy" bindinput="onInput"/>
            </view>
          </view>

          <!-- 新房or二手房 -->
          <view class="form-item">
            <view class="form-label">新房or二手房</view>
            <view class="radio-group">
              <view
                class="radio-item {{buyFormData.houseAge === '新房' ? 'selected' : ''}}"
                bindtap="onRadioSelect"
                data-field="houseAge"
                data-value="新房"
                data-type="buy"
              >
                <view class="radio-circle">
                  <view wx:if="{{buyFormData.houseAge === '新房'}}" class="radio-dot"></view>
                </view>
                <text class="radio-text">新房</text>
              </view>
              <view
                class="radio-item {{buyFormData.houseAge === '二手房' ? 'selected' : ''}}"
                bindtap="onRadioSelect"
                data-field="houseAge"
                data-value="二手房"
                data-type="buy"
              >
                <view class="radio-circle">
                  <view wx:if="{{buyFormData.houseAge === '二手房'}}" class="radio-dot"></view>
                </view>
                <text class="radio-text">二手房</text>
              </view>
            </view>
          </view>

          <!-- 建筑面积 -->
          <view class="form-item">
            <view class="form-label">建筑面积m²</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入建筑面积" type="digit" value="{{buyFormData.buildingArea}}" data-field="buildingArea" data-type="buy" bindinput="onInput"/>
            </view>
          </view>

          <!-- 户型 -->
          <picker range="{{houseTypeOptions}}" value="{{buyPickerIndexes.houseType}}" data-field="houseType" data-type="buy" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">户型</view>
              <view class="form-value-container">
                <text class="form-value">{{buyFormData.houseType || '请选择户型'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>

          <!-- 朝向 -->
          <view class="form-item">
            <view class="form-label">朝向</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入朝向" value="{{buyFormData.orientation}}" data-field="orientation" data-type="buy" bindinput="onInput"/>
            </view>
          </view>

          <!-- 楼层 -->
          <view class="form-item">
            <view class="form-label">楼层</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入楼层" value="{{buyFormData.floor}}" data-field="floor" data-type="buy" bindinput="onInput"/>
            </view>
          </view>

          <!-- 装修情况 -->
          <picker range="{{decorationOptions}}" value="{{buyPickerIndexes.decoration}}" data-field="decoration" data-type="buy" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">装修情况</view>
              <view class="form-value-container">
                <text class="form-value">{{buyFormData.decoration || '请选择装修情况'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>

          <!-- 景观/视野 -->
          <view class="form-item">
            <view class="form-label">景观/视野</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请描述景观视野" value="{{buyFormData.view}}" data-field="view" data-type="buy" bindinput="onInput"/>
            </view>
          </view>

          <!-- 目前状态 -->
          <picker range="{{buyCurrentStatusOptions}}" value="{{buyPickerIndexes.currentStatus}}" data-field="currentStatus" data-type="buy" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">目前状态</view>
              <view class="form-value-container">
                <text class="form-value">{{buyFormData.currentStatus || '请选择目前状态'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>

          <!-- 房子总价 -->
          <view class="form-item">
            <view class="form-label">房子总价(万)</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入房子总价" type="digit" value="{{buyFormData.totalPrice}}" data-field="totalPrice" data-type="buy" bindinput="onInput"/>
            </view>
          </view>

          <!-- 税费 -->
          <view class="form-item">
            <view class="form-label">税费(万)</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入税费金额" type="digit" value="{{buyFormData.taxes}}" data-field="taxes" data-type="buy" bindinput="onInput"/>
            </view>
          </view>

          <!-- 中介费 -->
          <view class="form-item" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-label">中介费(万)</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入中介费金额" type="digit" value="{{buyFormData.agencyFee}}" data-field="agencyFee" data-type="buy" bindinput="onInput"/>
            </view>
          </view>
        </view>

        <!-- 备注信息 -->
        <view class="form-section">
          <view class="section-title">个人补充说明</view>
          <view class="form-item textarea-item">
            <textarea class="form-textarea" placeholder="请补充其他相关信息（可选）" value="{{buyFormData.additionalInfo}}" data-field="additionalInfo" data-type="buy" bindinput="onInput" maxlength="500" style="height: 135rpx; display: block; box-sizing: border-box;"></textarea>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container">
        <button class="submit-btn" bindtap="submitBuyForm" style="width: 650rpx;">提交评测</button>
      </view>
    </view>

    <!-- 卖房版内容 -->
    <view class="content-panel {{currentTab === 1 ? 'show' : 'hide'}}">
      <!-- 表单区域 -->
      <view class="form-container">
        <!-- 房屋信息表单 -->
        <view class="form-section">
          <!-- 提示文字 -->
          <view class="submit-record">
            <text class="record-text">请根据您的实际情况填写，提供的信息越详细，评估结果越准确。</text>
          </view>
          
          <!-- 小区名称 -->
          <view class="form-item">
            <view class="form-label">小区名称</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入小区名称" value="{{sellFormData.communityName}}" data-field="communityName" data-type="sell" bindinput="onInput"/>
            </view>
          </view>

          <!-- 房产类型 -->
          <picker range="{{propertyTypeOptions}}" value="{{sellPickerIndexes.propertyType}}" data-field="propertyType" data-type="sell" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">房产类型</view>
              <view class="form-value-container">
                <text class="form-value">{{sellFormData.propertyType || '请选择房产类型'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>

          <!-- 其他房产类型输入 -->
          <view wx:if="{{sellFormData.propertyType === '其他'}}" class="form-item">
            <view class="form-label">请具体说明</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请具体说明房产类型" value="{{sellFormData.otherPropertyType}}" data-field="otherPropertyType" data-type="sell" bindinput="onInput"/>
            </view>
          </view>

          <!-- 建筑面积 -->
          <view class="form-item">
            <view class="form-label">建筑面积m²</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入建筑面积" type="digit" value="{{sellFormData.buildingArea}}" data-field="buildingArea" data-type="sell" bindinput="onInput"/>
            </view>
          </view>

          <!-- 户型 -->
          <picker range="{{houseTypeOptions}}" value="{{sellPickerIndexes.houseType}}" data-field="houseType" data-type="sell" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">户型</view>
              <view class="form-value-container">
                <text class="form-value">{{sellFormData.houseType || '请选择户型'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>

          <!-- 朝向 -->
          <view class="form-item">
            <view class="form-label">朝向</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入朝向" value="{{sellFormData.orientation}}" data-field="orientation" data-type="sell" bindinput="onInput"/>
            </view>
          </view>

          <!-- 楼层 -->
          <view class="form-item">
            <view class="form-label">楼层</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入楼层" value="{{sellFormData.floor}}" data-field="floor" data-type="sell" bindinput="onInput"/>
            </view>
          </view>

          <!-- 购买时间 -->
          <picker mode="date" value="{{sellFormData.purchaseDate}}" data-type="sell" bindchange="onDateChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">购买时间</view>
              <view class="form-value-container">
                <text class="form-value">{{sellFormData.purchaseDate || '请选择购买时间'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>

          <!-- 买入总价 -->
          <view class="form-item">
            <view class="form-label">买入总价(万)</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入买入总价" type="digit" value="{{sellFormData.purchasePrice}}" data-field="purchasePrice" data-type="sell" bindinput="onInput"/>
            </view>
          </view>

          <!-- 剩余房贷 -->
          <view class="form-item">
            <view class="form-label">剩余房贷(万)</view>
            <view class="form-input-container">
              <input class="form-input" placeholder="请输入剩余房贷金额" type="digit" value="{{sellFormData.remainingLoan}}" data-field="remainingLoan" data-type="sell" bindinput="onInput"/>
            </view>
          </view>

          <!-- 装修情况 -->
          <picker range="{{decorationOptions}}" value="{{sellPickerIndexes.decoration}}" data-field="decoration" data-type="sell" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">装修情况</view>
              <view class="form-value-container">
                <text class="form-value">{{sellFormData.decoration || '请选择装修情况'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>

          <!-- 目前状态 -->
          <picker range="{{sellCurrentStatusOptions}}" value="{{sellPickerIndexes.currentStatus}}" data-field="currentStatus" data-type="sell" bindchange="onPickerChange" style="border-bottom: 1rpx solid rgb(204, 199, 199);">
            <view class="form-item">
              <view class="form-label">目前状态</view>
              <view class="form-value-container">
                <text class="form-value">{{sellFormData.currentStatus || '请选择目前状态'}}</text>
                <text class="arrow-text">></text>
              </view>
            </view>
          </picker>
        </view>

        <!-- 备注信息 -->
        <view class="form-section">
          <view class="section-title">个人补充说明</view>
          <view class="form-item textarea-item">
            <textarea class="form-textarea" placeholder="请补充其他相关信息（可选）" value="{{sellFormData.additionalInfo}}" data-field="additionalInfo" data-type="sell" bindinput="onInput" maxlength="500" style="height: 135rpx; display: block; box-sizing: border-box;"></textarea>
          </view>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container">
        <button class="submit-btn" bindtap="submitSellForm" style="width: 650rpx;">提交评测</button>
      </view>
    </view>
  </view>
</view>
