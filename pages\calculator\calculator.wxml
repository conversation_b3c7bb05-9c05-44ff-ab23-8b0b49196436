<!--pages/calculator/calculator.wxml-->
<view class="container">

  <!-- 主要内容区域 -->
  <view class="content">
    <!-- 选项卡 -->
    <view class="tab-container">
      <view class="tab-item {{currentTab == 0 ? 'active' : ''}}" bindtap="switchTab" data-index="0">
        <text>商业贷款</text>
      </view>
      <view class="tab-item {{currentTab == 1 ? 'active' : ''}}" bindtap="switchTab" data-index="1">
        <text>公积金贷款</text>
      </view>
      <view class="tab-item {{currentTab == 2 ? 'active' : ''}}" bindtap="switchTab" data-index="2">
        <text>组合贷款</text>
      </view>
    </view>

    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 按贷款总额模式 -->
      <view wx:if="{{currentTab == 0}}">
        <!-- 贷款金额 -->
        <view class="form-item">
          <view class="form-label">贷款金额</view>
          <view class="form-input-container">
            <input class="form-input" placeholder="请输入贷款金额" value="{{loanAmount}}" bindinput="onLoanAmountInput" type="digit"/>
            <text class="form-unit">万</text>
          </view>
        </view>
      </view>

      <!-- 公积金贷款模式 -->
      <view wx:if="{{currentTab == 1}}">
        <!-- 贷款金额 -->
        <view class="form-item">
          <view class="form-label">贷款金额</view>
          <view class="form-input-container">
            <input class="form-input" placeholder="请输入公积金贷款金额" value="{{fundLoanAmount}}" bindinput="onFundLoanAmountInput" type="digit"/>
            <text class="form-unit">万</text>
          </view>
        </view>
      </view>

      <!-- 组合贷款模式 -->
      <view wx:if="{{currentTab == 2}}">
        <!-- 商业贷款金额 -->
        <view class="form-item">
          <view class="form-label">商业贷款金额</view>
          <view class="form-input-container">
            <input class="form-input" placeholder="请输入商业贷款金额" value="{{commercialLoanAmount}}" bindinput="onCommercialLoanAmountInput" type="digit"/>
            <text class="form-unit">万</text>
          </view>
        </view>

        <!-- 公积金贷款金额 -->
        <view class="form-item">
          <view class="form-label">公积金贷款金额</view>
          <view class="form-input-container">
            <input class="form-input" placeholder="请输入公积金贷款金额" value="{{fundLoanAmount}}" bindinput="onFundLoanAmountInput" type="digit"/>
            <text class="form-unit">万</text>
          </view>
        </view>
      </view>

      <!-- 商贷年限 -->
      <picker range="{{yearOptions}}" value="{{yearIndex}}" bindchange="onYearChange">
        <view class="form-item">
          <view class="form-label">贷款期限</view>
          <view class="form-value-container">
            <text class="form-value">{{loanYears}}年({{loanYears * 12}}期)</text>
            <text class="arrow-text">></text>
          </view>
        </view>
      </picker>

      <!-- 商业贷款利率 -->
      <view wx:if="{{currentTab == 0 || currentTab == 2}}">
        <!-- 预设利率选择 -->
        <picker wx:if="{{!isCustomRate}}" range="{{rateOptions}}" value="{{rateIndex}}" bindchange="onRateChange">
          <view class="form-item">
            <view class="form-label">商贷利率</view>
            <view class="form-value-container">
              <text class="valueo">{{loanRate}}</text>
              <text class="form-unitop">%</text>
              <text class="form-select">选择</text>
              <text class="arrow-text">></text>
            </view>
          </view>
        </picker>

        <!-- 自定义利率输入 -->
        <view wx:if="{{isCustomRate}}" class="form-item">
          <view class="form-label">自定义利率</view>
          <view class="form-input-container">
            <input
              class="form-input custom-rate-input"
              placeholder="请输入利率"
              value="{{customLoanRate}}"
              bindinput="onCustomRateInput"
              type="digit"
              maxlength="6"
            />
            <text class="form-unit">%</text>
          </view>
        </view>

    
      </view>

      <!-- 公积金贷款利率 -->
      <view wx:if="{{currentTab == 1 || currentTab == 2}}">
        <!-- 预设公积金利率选择 -->
        <picker wx:if="{{!isCustomFundRate}}" range="{{fundRateOptions}}" value="{{fundRateIndex}}" bindchange="onFundRateChange">
          <view class="form-item">
            <view class="form-label">公积金利率</view>
            <view class="form-value-container">
              <text class="valueo">{{fundRate}}</text>
              <text class="form-unitop">%</text>
              <text class="form-select">选择</text>
              <text class="arrow-text">></text>
            </view>
          </view>
        </picker>

        <!-- 自定义公积金利率输入 -->
        <view wx:if="{{isCustomFundRate}}" class="form-item">
          <view class="form-label">自定义公积金利率</view>
          <view class="form-input-container">
            <input
              class="form-input custom-rate-input"
              placeholder="请输入公积金利率"
              value="{{customFundRate}}"
              bindinput="onCustomFundRateInput"
              type="digit"
              maxlength="6"
            />
            <text class="form-unit">%</text>
          </view>
        </view>     
      </view>
    </view>

    <!-- 计算按钮 -->
    <view class="calculate-btn" bindtap="calculate">
      <text>开始计算</text>
    </view>



    <!-- 常见问题 -->
    <view class="faq-link" bindtap="showFAQ">
      <text>常见问题解决方案</text>
    </view>

    <!-- 管理员入口（长按显示） -->
  </view>
</view>


