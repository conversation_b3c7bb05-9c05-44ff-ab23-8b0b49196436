// pages/calculator/calculator.js
const lprApiManager = require('../../utils/lprApi.js');

Page({
  data: {
    currentTab: 0, // 当前选中的选项卡：0-商业贷款，1-公积金贷款，2-组合贷款
    loanAmount: '', // 商业贷款金额
    fundLoanAmount: '', // 公积金贷款金额
    commercialLoanAmount: '', // 组合贷款中的商业贷款金额
    loanYears: 30, // 贷款年限
    loanRate: 3.6, // 商业贷款利率（默认最新LPR，30年对应3.6%）
    fundRate: 3.1, // 公积金贷款利率（默认3.1%）

    // 动态利率管理相关
    isCustomRate: false, // 是否使用自定义商业贷款利率
    customLoanRate: '', // 自定义商业贷款利率
    isCustomFundRate: false, // 是否使用自定义公积金利率
    customFundRate: '', // 自定义公积金利率
    currentLpr: 3.6, // 当前LPR基准利率
    lprUpdateTime: '', // LPR更新时间
    showLprInfo: true, // 是否显示LPR信息
    showFundRateInfo: true, // 是否显示公积金利率信息
    lprCache: null, // LPR数据缓存
    lprCacheTime: 0, // 缓存时间戳

    // 公积金利率信息
    fundRateInfo: {
      rate5Below: 3.1, // 5年以下公积金利率
      rate5Above: 3.6  // 5年以上公积金利率
    },

    // 选择器相关
    showYearPicker: false,
    showRateTypePicker: false,
    showRatePicker: false,

    // 年限选项
    yearOptions: ['1年', '2年', '3年', '4年', '5年', '6年', '7年', '8年', '9年', '10年',
                  '11年', '12年', '13年', '14年', '15年', '16年', '17年', '18年', '19年', '20年',
                  '21年', '22年', '23年', '24年', '25年', '26年', '27年', '28年', '29年', '30年'],
    yearIndex: 29, // 默认选中30年

    // 利率类型选项
    rateTypeOptions: ['使用官方LPR', '按旧版基准利率', '自定义利率'],
    rateTypeIndex: 0,

    // 商业贷款利率选项（默认为5-30年配置）
    rateOptions: ['3.6%（最新LPR）', '3.2%（首套利率）', '3.2%（二套利率）', '自定义利率'],
    rateIndex: 0, // 默认选中第一个选项

    // 公积金利率类型选项
    fundRateTypeOptions: ['使用官方利率', '自定义利率'],
    fundRateTypeIndex: 0,

    // 公积金贷款利率选项
    fundRateOptions: ['3.1%（5年以下）', '3.6%（5年以上）', '自定义利率'],
    fundRateIndex: 1, // 默认选中5年以上利率

    // 计算结果显示
    showResult: false,

    // 商业贷款计算结果
    commercialMonthlyPayment: 0,
    commercialTotalInterest: 0,
    commercialTotalPayment: 0,

    // 公积金贷款计算结果
    fundMonthlyPayment: 0,
    fundTotalInterest: 0,
    fundTotalPayment: 0,

    // 组合贷款汇总结果
    totalLoanAmount: 0,
    totalMonthlyPayment: 0,
    totalInterest: 0,
    totalPayment: 0
  },

  /**
   * 从后台API获取最新LPR利率数据
   */
  fetchLprFromAPI: async function() {
    console.log('开始获取LPR利率数据');

    try {
      // 使用LPR API管理器获取数据
      const lprData = await lprApiManager.getLatestLpr();

      if (lprData) {
        this.updateLprData(lprData);
        console.log('LPR数据获取成功:', lprData);
      } else {
        throw new Error('LPR数据为空');
      }
    } catch (error) {
      console.error('获取LPR数据失败:', error);
      // 使用默认数据
      this.useDefaultLprData();
    }
  },

  /**
   * 获取LPR历史记录
   */
  fetchLprHistory: async function() {
    try {
      const historyData = await lprApiManager.getLprHistory(12);
      console.log('LPR历史记录获取成功:', historyData);
      return historyData;
    } catch (error) {
      console.error('获取LPR历史记录失败:', error);
      return [];
    }
  },

  /**
   * 管理员更新LPR利率（需要管理员权限）
   */
  updateLprByAdmin: async function(newLprData) {
    try {
      const result = await lprApiManager.updateLpr(newLprData);

      if (result.statusCode === 200) {
        // 更新成功，刷新本地数据
        await this.fetchLprFromAPI();

        wx.showToast({
          title: 'LPR利率更新成功',
          icon: 'success'
        });

        console.log('LPR利率更新成功');
      } else {
        throw new Error('更新失败');
      }
    } catch (error) {
      console.error('更新LPR利率失败:', error);

      wx.showToast({
        title: '更新失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 更新LPR数据到页面
   */
  updateLprData: function(lprData) {
    this.setData({
      currentLpr: lprData.lpr5Year,
      lprUpdateTime: lprData.updateTime
    });

    // 更新利率选项
    this.updateRateOptions();

    console.log('LPR数据已更新:', lprData);
  },

  /**
   * 使用默认LPR数据
   */
  useDefaultLprData: function() {
    const defaultData = {
      lpr1Year: 3.1,
      lpr5Year: 3.6,
      updateTime: '2024-12-20'
    };

    this.updateLprData(defaultData);
    console.log('使用默认LPR数据');
  },

  /**
   * 根据贷款年限获取对应的LPR利率配置
   * @param {number} years 贷款年限
   * @returns {object} 包含利率选项和数值的配置对象
   */
  getRateConfigByYears: function(years) {
    const currentLpr = this.data.currentLpr;

    if (years >= 1 && years <= 4) {
      // 1-4年利率配置
      return {
        rateOptions: [`${currentLpr - 0.5}%（最新LPR）`, `${currentLpr - 0.9}%（首套利率）`, `${currentLpr - 0.9}%（二套利率）`, '自定义利率'],
        rates: [currentLpr - 0.5, currentLpr - 0.9, currentLpr - 0.9, 0],
        defaultRate: currentLpr - 0.5
      };
    } else if (years >= 5 && years <= 30) {
      // 5-30年利率配置
      return {
        rateOptions: [`${currentLpr}%（最新LPR）`, `${currentLpr - 0.4}%（首套利率）`, `${currentLpr - 0.4}%（二套利率）`, '自定义利率'],
        rates: [currentLpr, currentLpr - 0.4, currentLpr - 0.4, 0],
        defaultRate: currentLpr
      };
    } else {
      // 默认配置（异常情况）
      return {
        rateOptions: [`${currentLpr}%（最新LPR）`, `${currentLpr - 0.4}%（首套利率）`, `${currentLpr - 0.4}%（二套利率）`, '自定义利率'],
        rates: [currentLpr, currentLpr - 0.4, currentLpr - 0.4, 0],
        defaultRate: currentLpr
      };
    }
  },

  /**
   * 获取旧版基准利率配置
   * @returns {object} 包含13个旧版基准利率选项和数值的配置对象
   */
  getOldBaseRateConfig: function() {
    const baseRate = 4.35; // 旧版基准利率
    return {
      rateOptions: [
        '旧版基准利率（4.35%）',
        '旧版基准利率7折（3.05%）',
        '旧版基准利率7.5折（3.26%）',
        '旧版基准利率8折（3.48%）',
        '旧版基准利率8.5折（3.7%）',
        '旧版基准利率9折（3.92%）',
        '旧版基准利率9.5折（4.13%）',
        '旧版基准利率1.05倍（4.57%）',
        '旧版基准利率1.1倍（4.79%）',
        '旧版基准利率1.15倍（5%）',
        '旧版基准利率1.2倍（5.22%）',
        '旧版基准利率1.25倍（5.44%）',
        '旧版基准利率1.3倍（5.66%）',
        '自定义利率'
      ],
      rates: [
        4.35,  // 基准利率
        3.05,  // 7折
        3.26,  // 7.5折
        3.48,  // 8折
        3.7,   // 8.5折
        3.92,  // 9折
        4.13,  // 9.5折
        4.57,  // 1.05倍
        4.79,  // 1.1倍
        5.0,   // 1.15倍
        5.22,  // 1.2倍
        5.44,  // 1.25倍
        5.66,  // 1.3倍
        0      // 自定义利率占位符
      ],
      defaultRate: 4.35
    };
  },

  /**
   * 更新利率选项配置
   * @param {number} years 贷款年限（可选，仅在LPR模式下使用）
   */
  updateRateOptions: function(years) {
    const rateTypeIndex = this.data.rateTypeIndex;
    let rateConfig;
    let isCustomRate = false;
    let showLprInfo = false;

    if (rateTypeIndex === 0) {
      // 使用官方LPR模式
      const currentYears = years || this.data.loanYears;
      rateConfig = this.getRateConfigByYears(currentYears);
      showLprInfo = true;
      console.log('更新LPR利率配置 - 年限:', currentYears);
    } else if (rateTypeIndex === 1) {
      // 按旧版基准利率模式
      rateConfig = this.getOldBaseRateConfig();
      console.log('更新旧版基准利率配置');
    } else if (rateTypeIndex === 2) {
      // 自定义利率模式
      isCustomRate = true;
      rateConfig = { rateOptions: [], rates: [], defaultRate: 0 };
      console.log('切换到自定义利率模式');
    }

    const currentRateIndex = this.data.rateIndex;

    // 保持当前选中的利率索引，如果索引有效的话
    let newRateIndex = 0;
    let newLoanRate = rateConfig.defaultRate;

    if (!isCustomRate && currentRateIndex >= 0 && currentRateIndex < rateConfig.rates.length) {
      newRateIndex = currentRateIndex;
      newLoanRate = rateConfig.rates[currentRateIndex];
    }

    // 如果是自定义利率模式，使用自定义利率值
    if (isCustomRate && this.data.customLoanRate) {
      newLoanRate = parseFloat(this.data.customLoanRate);
    }

    console.log('新利率选项数量:', rateConfig.rateOptions.length);
    console.log('保持利率索引:', newRateIndex, '新利率值:', newLoanRate);

    this.setData({
      rateOptions: rateConfig.rateOptions,
      rateIndex: newRateIndex,
      loanRate: newLoanRate,
      isCustomRate: isCustomRate,
      showLprInfo: showLprInfo
    });
  },

  /**
   * 自定义利率输入处理
   */
  onCustomRateInput: function(e) {
    const value = this.validateRateInput(e.detail.value);

    this.setData({
      customLoanRate: value
    });

    // 实时更新贷款利率
    if (value && !isNaN(parseFloat(value))) {
      const rate = parseFloat(value);
      if (this.isValidRate(rate)) {
        this.setData({
          loanRate: rate
        });
      }
    }

    console.log('自定义利率输入:', value);
  },

  /**
   * 验证利率输入
   */
  validateRateInput: function(value) {
    // 只允许数字和一个小数点
    let cleanValue = value.replace(/[^\d.]/g, '');

    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }

    // 限制小数点后最多3位
    if (parts.length === 2 && parts[1].length > 3) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 3);
    }

    // 限制整数部分最多2位
    if (parts[0].length > 2) {
      cleanValue = parts[0].substring(0, 2) + (parts[1] ? '.' + parts[1] : '');
    }

    return cleanValue;
  },

  /**
   * 验证利率是否在合理范围内
   */
  isValidRate: function(rate) {
    return rate >= 0.1 && rate <= 20.0;
  },

  /**
   * 公积金利率类型选择变化
   */
  onFundRateTypeChange: function(e) {
    const index = parseInt(e.detail.value);
    console.log('公积金利率类型选择变化 - 索引:', index, '类型:', this.data.fundRateTypeOptions[index]);

    // 验证索引范围
    if (index >= 0 && index < this.data.fundRateTypeOptions.length) {
      this.setData({
        fundRateTypeIndex: index
      });

      // 根据新的利率类型更新公积金利率选项
      this.updateFundRateOptions();

      // 如果切换到自定义利率，清空之前的自定义值
      if (index === 1) {
        this.setData({
          customFundRate: '',
          fundRate: 0
        });
      }

      console.log('公积金利率类型更新成功 - 当前类型:', this.data.fundRateTypeOptions[index]);
    } else {
      console.error('公积金利率类型选择索引超出范围:', index);
    }
  },

  /**
   * 更新公积金利率选项配置
   */
  updateFundRateOptions: function() {
    const fundRateTypeIndex = this.data.fundRateTypeIndex;
    let isCustomFundRate = false;
    let showFundRateInfo = false;

    if (fundRateTypeIndex === 0) {
      // 使用官方利率模式
      showFundRateInfo = true;
      console.log('更新公积金官方利率配置');
    } else if (fundRateTypeIndex === 1) {
      // 自定义利率模式
      isCustomFundRate = true;
      console.log('切换到自定义公积金利率模式');
    }

    // 如果是自定义利率模式，使用自定义利率值
    let newFundRate = this.data.fundRate;
    if (isCustomFundRate && this.data.customFundRate) {
      newFundRate = parseFloat(this.data.customFundRate);
    } else if (!isCustomFundRate) {
      // 使用预设利率
      const fundRates = [3.1, 3.6]; // 对应公积金利率选项
      const currentFundRateIndex = this.data.fundRateIndex;
      if (currentFundRateIndex >= 0 && currentFundRateIndex < fundRates.length) {
        newFundRate = fundRates[currentFundRateIndex];
      }
    }

    this.setData({
      isCustomFundRate: isCustomFundRate,
      showFundRateInfo: showFundRateInfo,
      fundRate: newFundRate
    });
  },

  /**
   * 自定义公积金利率输入处理
   */
  onCustomFundRateInput: function(e) {
    const value = this.validateRateInput(e.detail.value);

    this.setData({
      customFundRate: value
    });

    // 实时更新公积金利率
    if (value && !isNaN(parseFloat(value))) {
      const rate = parseFloat(value);
      if (this.isValidRate(rate)) {
        this.setData({
          fundRate: rate
        });
      }
    }

    console.log('自定义公积金利率输入:', value);
  },

  /**
   * 返回商贷利率选择器
   */
  backToRateSelect: function() {
    this.setData({
      isCustomRate: false,
      rateIndex: 0,
      loanRate: this.data.rateOptions.length > 0 ?
        (this.data.rateTypeIndex === 0 ?
          this.getRateConfigByYears(this.data.loanYears).rates[0] :
          this.getOldBaseRateConfig().rates[0]) : 0
    });
    console.log('返回商贷利率选择器');
  },

  /**
   * 返回公积金利率选择器
   */
  backToFundRateSelect: function() {
    this.setData({
      isCustomFundRate: false,
      fundRateIndex: 1, // 默认选择5年以上利率
      fundRate: 3.6
    });
    console.log('返回公积金利率选择器');
  },

  onLoad: function (options) {
    // 页面加载时的逻辑
    console.log('房贷计算页面加载');
    console.log('初始currentTab状态:', this.data.currentTab);

    // 强制重新设置初始状态，确保数据类型正确
    this.setData({
      currentTab: 0,
      loanAmount: '',
      housePrice: '',
      loanRatio: 7,
      calculatedLoanAmount: '',
      // 确保年限选择器的初始状态正确
      loanYears: 30,
      yearIndex: 29,  // 30年对应索引29
      // 初始化动态利率相关数据
      lprUpdateTime: this.getCurrentDate()
    });

    // 获取最新LPR数据
    this.fetchLprFromAPI();

    // 初始化公积金利率选项
    this.updateFundRateOptions();

    console.log('设置后currentTab状态:', this.data.currentTab);
    console.log('设置后年限状态:', this.data.loanYears, '索引:', this.data.yearIndex);
  },

  /**
   * 获取当前日期字符串
   */
  getCurrentDate: function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  },

  onReady: function() {
    // 页面渲染完成后再次确认状态
    console.log('页面渲染完成，currentTab:', this.data.currentTab);
  },

  // 返回上一页或首页
  goBack: function() {
    // 获取页面栈
    const pages = getCurrentPages();

    // 如果页面栈中只有当前页面，说明是直接进入的，返回首页
    if (pages.length <= 1) {
      wx.reLaunch({
        url: '/pages/index/index'
      });
    } else {
      // 否则返回上一页
      wx.navigateBack();
    }
  },

  // 切换选项卡
  switchTab: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    console.log('切换选项卡到:', index);
    this.setData({
      currentTab: index
    });
    console.log('当前选项卡状态:', this.data.currentTab);
  },

  // 商业贷款金额输入
  onLoanAmountInput: function(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      loanAmount: value
    });
  },

  // 公积金贷款金额输入
  onFundLoanAmountInput: function(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      fundLoanAmount: value
    });
  },

  // 组合贷款中的商业贷款金额输入
  onCommercialLoanAmountInput: function(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      commercialLoanAmount: value
    });
  },

  // 验证数字输入（支持小数点）
  validateNumberInput: function(value) {
    // 只允许数字和一个小数点
    let cleanValue = value.replace(/[^\d.]/g, '');

    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }

    // 限制小数点后最多2位
    if (parts.length === 2 && parts[1].length > 2) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 2);
    }

    return cleanValue;
  },

  // 房屋总价输入
  onHousePriceInput: function(e) {
    const housePrice = this.validateNumberInput(e.detail.value);
    this.setData({
      housePrice: housePrice
    });
    // 自动计算贷款金额
    this.calculateLoanAmount();
  },

  // 计算出的贷款金额输入（手动修改）
  onCalculatedLoanAmountInput: function(e) {
    const value = this.validateNumberInput(e.detail.value);
    this.setData({
      calculatedLoanAmount: value
    });
  },

  // 贷款比例输入
  onLoanRatioInput: function(e) {
    const value = this.validateLoanRatioInput(e.detail.value);
    this.setData({
      loanRatio: value
    });
    // 重新计算贷款金额
    this.calculateLoanAmount();
  },

  // 验证贷款比例输入
  validateLoanRatioInput: function(value) {
    // 只允许数字和一个小数点
    let cleanValue = value.replace(/[^\d.]/g, '');

    // 确保只有一个小数点
    const parts = cleanValue.split('.');
    if (parts.length > 2) {
      cleanValue = parts[0] + '.' + parts.slice(1).join('');
    }

    // 限制小数点后最多1位
    if (parts.length === 2 && parts[1].length > 1) {
      cleanValue = parts[0] + '.' + parts[1].substring(0, 1);
    }

    // 限制范围在0.1-9.9之间
    const numValue = parseFloat(cleanValue);
    if (numValue > 9.9) {
      cleanValue = '9.9';
    } else if (numValue < 0.1 && cleanValue !== '' && cleanValue !== '0' && cleanValue !== '0.') {
      cleanValue = '0.1';
    }

    return cleanValue;
  },

  // 自动计算贷款金额
  calculateLoanAmount: function() {
    const housePrice = parseFloat(this.data.housePrice);
    const loanRatio = parseFloat(this.data.loanRatio);

    if (housePrice && loanRatio) {
      const calculatedAmount = (housePrice * loanRatio / 10).toFixed(1);
      this.setData({
        calculatedLoanAmount: calculatedAmount
      });
    }
  },

  // 显示年限选择器
  showYearPicker: function() {
    console.log('显示年限选择器');
    this.setData({
      showYearPicker: true
    });
    console.log('年限选择器状态:', this.data.showYearPicker);
  },

  // 隐藏年限选择器
  hideYearPicker: function() {
    this.setData({
      showYearPicker: false
    });
  },

  // 年限选择变化
  onYearChange: function(e) {
    const index = parseInt(e.detail.value);
    const years = index + 1; // 1年对应index 0，30年对应index 29
    console.log('年限选择变化 - 索引:', index, '年限:', years);
    console.log('年限选项:', this.data.yearOptions[index]);

    // 验证索引范围
    if (index >= 0 && index < this.data.yearOptions.length) {
      this.setData({
        yearIndex: index,
        loanYears: years
      });
      console.log('年限更新成功 - 当前年限:', this.data.loanYears, '期数:', this.data.loanYears * 12);

      // 根据新的年限更新利率选项
      this.updateRateOptions(years);
    } else {
      console.error('年限选择索引超出范围:', index);
    }
  },

  // 显示利率类型选择器
  showRateTypePicker: function() {
    console.log('显示利率类型选择器');
    this.setData({
      showRateTypePicker: true
    });
    console.log('利率类型选择器状态:', this.data.showRateTypePicker);
  },

  // 隐藏利率类型选择器
  hideRateTypePicker: function() {
    this.setData({
      showRateTypePicker: false
    });
  },

  // 利率类型选择变化
  onRateTypeChange: function(e) {
    const index = parseInt(e.detail.value);
    console.log('利率类型选择变化 - 索引:', index, '类型:', this.data.rateTypeOptions[index]);

    // 验证索引范围
    if (index >= 0 && index < this.data.rateTypeOptions.length) {
      this.setData({
        rateTypeIndex: index
      });

      // 根据新的利率类型更新利率选项
      this.updateRateOptions();

      // 如果切换到自定义利率，清空之前的自定义值
      if (index === 2) {
        this.setData({
          customLoanRate: '',
          loanRate: 0
        });
      }

      console.log('利率类型更新成功 - 当前类型:', this.data.rateTypeOptions[index]);
    } else {
      console.error('利率类型选择索引超出范围:', index);
    }
  },

  // 显示利率选择器
  showRatePicker: function() {
    console.log('显示利率选择器');
    this.setData({
      showRatePicker: true
    });
    console.log('利率选择器状态:', this.data.showRatePicker);
  },

  // 隐藏利率选择器
  hideRatePicker: function() {
    this.setData({
      showRatePicker: false
    });
  },

  // 利率选择变化
  onRateChange: function(e) {
    const index = parseInt(e.detail.value);
    const rateTypeIndex = this.data.rateTypeIndex;
    let rateConfig;

    // 根据利率类型获取对应的利率配置
    if (rateTypeIndex === 0) {
      // 按最新LPR模式
      rateConfig = this.getRateConfigByYears(this.data.loanYears);
      console.log('利率选择变化 - LPR模式，索引:', index, '年限:', this.data.loanYears);
    } else {
      // 按旧版基准利率模式
      rateConfig = this.getOldBaseRateConfig();
      console.log('利率选择变化 - 旧版基准利率模式，索引:', index);
    }

    const rates = rateConfig.rates;
    console.log('选择利率:', rates[index], '选项:', this.data.rateOptions[index]);

    // 验证索引范围
    if (index >= 0 && index < rates.length) {
      // 检查是否选择了自定义利率（最后一个选项）
      const isCustomRateSelected = index === rates.length - 1;

      if (isCustomRateSelected) {
        // 切换到自定义利率模式
        this.setData({
          rateIndex: index,
          isCustomRate: true,
          customLoanRate: '',
          loanRate: 0
        });
        console.log('切换到自定义商贷利率模式');
      } else {
        // 使用预设利率
        this.setData({
          rateIndex: index,
          isCustomRate: false,
          loanRate: rates[index]
        });
        console.log('利率更新成功 - 当前利率:', this.data.loanRate + '%');
      }
    } else {
      console.error('利率选择索引超出范围:', index, '最大索引:', rates.length - 1);
    }
  },

  // 公积金利率选择变化
  onFundRateChange: function(e) {
    const index = parseInt(e.detail.value);
    const fundRates = [3.1, 3.6, 0]; // 对应公积金利率选项，最后一个是自定义利率占位符

    console.log('公积金利率选择变化，索引:', index);

    if (index >= 0 && index < fundRates.length) {
      // 检查是否选择了自定义利率（最后一个选项）
      const isCustomFundRateSelected = index === fundRates.length - 1;

      if (isCustomFundRateSelected) {
        // 切换到自定义公积金利率模式
        this.setData({
          fundRateIndex: index,
          isCustomFundRate: true,
          customFundRate: '',
          fundRate: 0
        });
        console.log('切换到自定义公积金利率模式');
      } else {
        // 使用预设公积金利率
        this.setData({
          fundRateIndex: index,
          isCustomFundRate: false,
          fundRate: fundRates[index]
        });
        console.log('公积金利率更新成功 - 当前利率:', this.data.fundRate + '%');
      }
    } else {
      console.error('公积金利率选择索引超出范围:', index);
    }
  },

  // 开始计算
  calculate: function() {
    console.log('开始计算，当前标签页:', this.data.currentTab);

    // 根据当前选项卡进行不同的验证和跳转
    if (this.data.currentTab === 0) {
      // 商业贷款验证和跳转
      this.validateAndNavigateCommercial();
    } else if (this.data.currentTab === 1) {
      // 公积金贷款验证和跳转
      this.validateAndNavigateFund();
    } else if (this.data.currentTab === 2) {
      // 组合贷款验证和跳转
      this.validateAndNavigateCombined();
    }
  },

  // 商业贷款验证和跳转
  validateAndNavigateCommercial: function() {
    console.log('商业贷款验证开始，当前数据:', {
      loanAmount: this.data.loanAmount,
      loanYears: this.data.loanYears,
      loanRate: this.data.loanRate,
      isCustomRate: this.data.isCustomRate,
      customLoanRate: this.data.customLoanRate
    });

    if (!this.data.loanAmount) {
      wx.showToast({
        title: '请输入商业贷款金额',
        icon: 'none'
      });
      return;
    }

    if (!this.data.loanYears || this.data.loanYears <= 0) {
      wx.showToast({
        title: '请选择贷款年限',
        icon: 'none'
      });
      return;
    }

    // 验证利率
    if (this.data.isCustomRate) {
      // 自定义利率验证
      if (!this.data.customLoanRate) {
        wx.showToast({
          title: '请输入自定义利率',
          icon: 'none'
        });
        return;
      }

      const customRate = parseFloat(this.data.customLoanRate);
      if (isNaN(customRate) || !this.isValidRate(customRate)) {
        wx.showToast({
          title: '利率范围应在0.1%-20%之间',
          icon: 'none'
        });
        return;
      }
    } else {
      // 预设利率验证
      if (!this.data.loanRate || this.data.loanRate <= 0) {
        wx.showToast({
          title: '请选择贷款利率',
          icon: 'none'
        });
        return;
      }
    }

    // 跳转到结果页面
    const params = {
      loanType: 'commercial',
      loanAmount: this.data.loanAmount,
      loanYears: this.data.loanYears,
      interestRate: this.data.loanRate,
      rateType: this.data.rateTypeOptions[this.data.rateTypeIndex]
    };

    console.log('商业贷款验证通过，准备跳转，参数:', params);
    this.navigateToResult(params);
  },

  // 公积金贷款验证和跳转
  validateAndNavigateFund: function() {
    console.log('公积金贷款验证开始，当前数据:', {
      fundLoanAmount: this.data.fundLoanAmount,
      loanYears: this.data.loanYears,
      fundRate: this.data.fundRate,
      isCustomFundRate: this.data.isCustomFundRate,
      customFundRate: this.data.customFundRate
    });

    if (!this.data.fundLoanAmount) {
      wx.showToast({
        title: '请输入公积金贷款金额',
        icon: 'none'
      });
      return;
    }

    if (!this.data.loanYears || this.data.loanYears <= 0) {
      wx.showToast({
        title: '请选择贷款年限',
        icon: 'none'
      });
      return;
    }

    // 验证公积金利率
    if (this.data.isCustomFundRate) {
      // 自定义公积金利率验证
      if (!this.data.customFundRate) {
        wx.showToast({
          title: '请输入自定义公积金利率',
          icon: 'none'
        });
        return;
      }

      const customFundRate = parseFloat(this.data.customFundRate);
      if (isNaN(customFundRate) || !this.isValidRate(customFundRate)) {
        wx.showToast({
          title: '公积金利率范围应在0.1%-20%之间',
          icon: 'none'
        });
        return;
      }
    } else {
      // 预设公积金利率验证
      if (!this.data.fundRate || this.data.fundRate <= 0) {
        wx.showToast({
          title: '请选择公积金贷款利率',
          icon: 'none'
        });
        return;
      }
    }

    // 跳转到结果页面
    const params = {
      loanType: 'fund',
      loanAmount: this.data.fundLoanAmount,
      loanYears: this.data.loanYears,
      interestRate: this.data.fundRate,
      fundRateType: this.data.fundRateTypeOptions[this.data.fundRateTypeIndex]
    };

    console.log('公积金贷款验证通过，准备跳转，参数:', params);
    this.navigateToResult(params);
  },

  // 组合贷款验证和跳转
  validateAndNavigateCombined: function() {
    if (!this.data.commercialLoanAmount && !this.data.fundLoanAmount) {
      wx.showToast({
        title: '请输入贷款金额',
        icon: 'none'
      });
      return;
    }

    if (!this.data.loanYears || this.data.loanYears <= 0) {
      wx.showToast({
        title: '请选择贷款年限',
        icon: 'none'
      });
      return;
    }

    // 验证商业贷款利率
    if (this.data.commercialLoanAmount) {
      if (this.data.isCustomRate) {
        // 自定义商业贷款利率验证
        if (!this.data.customLoanRate) {
          wx.showToast({
            title: '请输入自定义商业贷款利率',
            icon: 'none'
          });
          return;
        }

        const customRate = parseFloat(this.data.customLoanRate);
        if (isNaN(customRate) || !this.isValidRate(customRate)) {
          wx.showToast({
            title: '商业贷款利率范围应在0.1%-20%之间',
            icon: 'none'
          });
          return;
        }
      } else {
        // 预设商业贷款利率验证
        if (!this.data.loanRate || this.data.loanRate <= 0) {
          wx.showToast({
            title: '请选择商业贷款利率',
            icon: 'none'
          });
          return;
        }
      }
    }

    // 验证公积金贷款利率
    if (this.data.fundLoanAmount) {
      if (this.data.isCustomFundRate) {
        // 自定义公积金利率验证
        if (!this.data.customFundRate) {
          wx.showToast({
            title: '请输入自定义公积金利率',
            icon: 'none'
          });
          return;
        }

        const customFundRate = parseFloat(this.data.customFundRate);
        if (isNaN(customFundRate) || !this.isValidRate(customFundRate)) {
          wx.showToast({
            title: '公积金利率范围应在0.1%-20%之间',
            icon: 'none'
          });
          return;
        }
      } else {
        // 预设公积金利率验证
        if (!this.data.fundRate || this.data.fundRate <= 0) {
          wx.showToast({
            title: '请选择公积金贷款利率',
            icon: 'none'
          });
          return;
        }
      }
    }

    // 跳转到结果页面
    const params = {
      loanType: 'combined',
      commercialAmount: this.data.commercialLoanAmount || 0,
      fundAmount: this.data.fundLoanAmount || 0,
      loanYears: this.data.loanYears,
      commercialRate: this.data.loanRate,
      fundRate: this.data.fundRate,
      commercialRateType: this.data.rateTypeOptions[this.data.rateTypeIndex],
      fundRateType: this.data.fundRateTypeOptions[this.data.fundRateTypeIndex]
    };

    console.log('组合贷款验证通过，准备跳转，参数:', params);
    this.navigateToResult(params);
  },

  // 跳转到结果页面
  navigateToResult: function(params) {
    console.log('开始构建跳转URL，参数:', params);

    // 构建URL参数
    let url = '/pages/calculation-result/calculation-result?';
    const urlParams = [];

    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        urlParams.push(`${key}=${encodeURIComponent(params[key])}`);
      }
    }

    url += urlParams.join('&');

    console.log('跳转到结果页面，URL长度:', url.length);
    console.log('跳转到结果页面，URL:', url);

    // 检查URL长度，微信小程序URL参数有长度限制
    if (url.length > 1000) {
      console.error('URL参数过长:', url.length);
      wx.showToast({
        title: 'URL参数过长，请简化输入',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: url,
      success: () => {
        console.log('成功跳转到房贷计算结果页面');
      },
      fail: (err) => {
        console.error('跳转失败详细信息:', err);
        wx.showToast({
          title: `跳转失败: ${err.errMsg || '未知错误'}`,
          icon: 'none',
          duration: 3000
        });
      }
    });
  },

  // 显示常见问题
  showFAQ: function() {
    wx.showModal({
      title: '常见问题',
      content: '1. 如何选择合适的贷款年限？\n2. LPR利率是什么？\n3. 等额本息和等额本金的区别？',
      showCancel: false
    });
  },

  // 显示管理员入口（长按触发）
  showAdminEntry: function() {
    wx.showModal({
      title: '管理员入口',
      content: '是否进入LPR利率管理页面？',
      confirmText: '进入',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/admin/lpr-management',
            fail: (err) => {
              console.error('跳转到管理员页面失败:', err);
              wx.showToast({
                title: '页面跳转失败',
                icon: 'none'
              });
            }
          });
        }
      }
    });
  }
});
