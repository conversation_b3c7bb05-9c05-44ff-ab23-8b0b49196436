/**terms.wxss**/
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 10rpx;
  background-color: #f5f5f5;
  box-sizing: border-box;
  overflow-y: auto;
}

/* 顶部状态栏 */
.status-bar {
  height: 88rpx;
  background-color: #000;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 28rpx;
  font-weight: 600;
  margin: 0 -20rpx 20rpx -20rpx;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.battery-icon {
  height: 24rpx;
  border: 2rpx solid white;
  border-radius: 4rpx;
  position: relative;
}

.battery-icon::after {
  content: '';
  position: absolute;
  right: -6rpx;
  top: 6rpx;
  width: 4rpx;
  height: 12rpx;
  background-color: white;
  border-radius: 0 2rpx 2rpx 0;
}

/* 页面标题区域 */
.page-header {
  height: 120rpx;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
  margin: 0 -20rpx 20rpx -20rpx;
  padding: 0 20rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.header-icons {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.icon-dots {
  display: flex;
  gap: 6rpx;
}

.dot {
  width: 8rpx;
  height: 8rpx;
  background-color: #999;
  border-radius: 50%;
}

.icon-circle {
  width: 40rpx;
  height: 40rpx;
  background-color: #333;
  border-radius: 50%;
}

/* 标签页切换 */
.tab-container {
  width: 100%;
  height: 100rpx;
  background-color: white;
  display: flex;
  border-bottom: 1rpx solid #f0f0f0;
  margin: 0 -20rpx 20rpx -20rpx;
  position: relative;
  z-index: 10;
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 100rpx;
}

.tab-text {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
}

.tab-item.active .tab-text {
  color: #FF4444;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #FF4444;
  border-radius: 3rpx;
}

/* 内容区域 */
.content-container {
  width: 100%;
  background-color: #f5f5f5;
  flex: 1;
  margin: -20rpx;
}

.content-panel {
  width: 100%;
}
.content-panel.show {
  display: block;
}

.content-panel.hide {
  display: none;
}

/* 内容项目 */
.content-item {
  width: 100%;
  background-color: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  margin-bottom: 5rpx;
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.item-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  position: relative;
  left: 20rpx;
}

.item-subtitle {
  font-size: 28rpx;
  color: #666;
  line-height: 1.3;
}

.item-date {
  font-size: 28rpx;
  color: #999;
  position: relative;
  left: 20rpx;
  display: flex;
  letter-spacing: 3rpx;
}

/* 图片容器样式 */
.image-container {
  width: 120rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  position: relative;
}

/* 图片样式 */
.item-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
  background-color: #e0e0e0;
  object-fit: cover;
  border: 2rpx solid #ddd;
  display: block;
}

/* 图片占位符样式 */
.image-placeholder {
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  border: 2rpx solid #ddd;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #999;
}

/* 响应式图片尺寸 */
@media (max-width: 400px) {
  .item-image {
    width: 100rpx;
    height: 70rpx;
  }
}

@media (min-width: 800px) {
  .item-image {
    width: 140rpx;
    height: 90rpx;
  }
}

/* 点击效果 */
.content-item:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
  transition: all 0.1s ease;
}
