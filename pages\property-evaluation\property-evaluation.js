// pages/property-evaluation/property-evaluation.js
Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentTab: 0, // 当前选中的标签页：0-买房版，1-卖房版

    // 买房版表单数据
    buyFormData: {
      communityName: '',
      propertyType: '',
      otherPropertyType: '',
      houseAge: '',
      buildingArea: '',
      houseType: '',
      orientation: '',
      floor: '',
      decoration: '',
      view: '',
      currentStatus: '',
      totalPrice: '',
      taxes: '',
      agencyFee: '',
      additionalInfo: ''
    },

    // 卖房版表单数据
    sellFormData: {
      communityName: '',
      propertyType: '',
      otherPropertyType: '',
      buildingArea: '',
      houseType: '',
      orientation: '',
      floor: '',
      purchaseDate: '',
      purchasePrice: '',
      remainingLoan: '',
      decoration: '',
      currentStatus: '',
      additionalInfo: ''
    },

    // 买房版选择器索引
    buyPickerIndexes: {
      propertyType: -1,
      houseType: -1,
      decoration: -1,
      currentStatus: -1
    },

    // 卖房版选择器索引
    sellPickerIndexes: {
      propertyType: -1,
      houseType: -1,
      decoration: -1,
      currentStatus: -1
    },

    // 选择器选项
    propertyTypeOptions: ['住宅', '公寓', '农民房', '其他'],
    houseTypeOptions: ['一房', '两房', '三房一卫', '三房两卫', '四房', '四房以上'],
    decorationOptions: ['毛坯', '简装', '精装'],
    buyCurrentStatusOptions: ['建设中', '现房', '业主自住', '业主出租', '空置'],
    sellCurrentStatusOptions: ['自住', '出租', '空置']
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('房产评测主页面加载');
    this.loadSavedData();
  },

  /**
   * 标签页切换
   */
  switchTab: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    console.log('切换标签页:', index);
    
    this.setData({
      currentTab: index
    });
    
    // 保存当前数据
    this.saveCurrentData();
  },

  /**
   * 加载本地保存的数据
   */
  loadSavedData: function() {
    try {
      // 加载买房版数据
      const buyData = wx.getStorageSync('buyEvaluationData');
      if (buyData) {
        this.setData({
          buyFormData: { ...this.data.buyFormData, ...buyData.formData },
          buyPickerIndexes: { ...this.data.buyPickerIndexes, ...buyData.pickerIndexes }
        });
        console.log('买房版数据加载成功');
      }

      // 加载卖房版数据
      const sellData = wx.getStorageSync('sellEvaluationData');
      if (sellData) {
        this.setData({
          sellFormData: { ...this.data.sellFormData, ...sellData.formData },
          sellPickerIndexes: { ...this.data.sellPickerIndexes, ...sellData.pickerIndexes }
        });
        console.log('卖房版数据加载成功');
      }
    } catch (error) {
      console.error('加载保存数据失败:', error);
    }
  },

  /**
   * 保存当前数据
   */
  saveCurrentData: function() {
    try {
      // 保存买房版数据
      wx.setStorageSync('buyEvaluationData', {
        formData: this.data.buyFormData,
        pickerIndexes: this.data.buyPickerIndexes
      });

      // 保存卖房版数据
      wx.setStorageSync('sellEvaluationData', {
        formData: this.data.sellFormData,
        pickerIndexes: this.data.sellPickerIndexes
      });
    } catch (error) {
      console.error('保存数据失败:', error);
    }
  },

  /**
   * 输入框输入处理
   */
  onInput: function(e) {
    const { field, type } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    if (type === 'buy') {
      this.setData({
        [`buyFormData.${field}`]: value
      });
    } else if (type === 'sell') {
      this.setData({
        [`sellFormData.${field}`]: value
      });
    }
    
    // 实时保存数据
    this.saveCurrentData();
  },

  /**
   * 选择器变化处理
   */
  onPickerChange: function(e) {
    const { field, type } = e.currentTarget.dataset;
    const index = parseInt(e.detail.value);
    
    if (type === 'buy') {
      const options = this.getOptionsArray(field);
      this.setData({
        [`buyPickerIndexes.${field}`]: index,
        [`buyFormData.${field}`]: options[index]
      });
    } else if (type === 'sell') {
      const options = this.getOptionsArray(field);
      this.setData({
        [`sellPickerIndexes.${field}`]: index,
        [`sellFormData.${field}`]: options[index]
      });
    }
    
    // 实时保存数据
    this.saveCurrentData();
  },

  /**
   * 单选按钮选择处理
   */
  onRadioSelect: function(e) {
    const { field, value, type } = e.currentTarget.dataset;
    
    if (type === 'buy') {
      this.setData({
        [`buyFormData.${field}`]: value
      });
    }
    
    // 实时保存数据
    this.saveCurrentData();
  },

  /**
   * 日期选择处理
   */
  onDateChange: function(e) {
    const { type } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    if (type === 'sell') {
      this.setData({
        'sellFormData.purchaseDate': value
      });
    }
    
    // 实时保存数据
    this.saveCurrentData();
  },

  /**
   * 获取选项数组
   */
  getOptionsArray: function(field) {
    switch (field) {
      case 'propertyType':
        return this.data.propertyTypeOptions;
      case 'houseType':
        return this.data.houseTypeOptions;
      case 'decoration':
        return this.data.decorationOptions;
      case 'currentStatus':
        return this.data.currentTab === 0 ? this.data.buyCurrentStatusOptions : this.data.sellCurrentStatusOptions;
      default:
        return [];
    }
  },

  /**
   * 提交买房版表单
   */
  submitBuyForm: function() {
    console.log('提交买房版表单');

    // 表单验证
    if (!this.validateBuyForm()) {
      return;
    }

    // 保存数据
    this.saveCurrentData();

    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交过程
    setTimeout(() => {
      wx.hideLoading();

      // 跳转到成功页面
      wx.navigateTo({
        url: '/pages/property-evaluation/success?type=buy',
        success: () => {
          // 清除保存的数据
          wx.removeStorageSync('buyEvaluationData');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });
        }
      });
    }, 1500);
  },

  /**
   * 提交卖房版表单
   */
  submitSellForm: function() {
    console.log('提交卖房版表单');

    // 表单验证
    if (!this.validateSellForm()) {
      return;
    }

    // 保存数据
    this.saveCurrentData();

    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交过程
    setTimeout(() => {
      wx.hideLoading();

      // 跳转到成功页面
      wx.navigateTo({
        url: '/pages/property-evaluation/success?type=sell',
        success: () => {
          // 清除保存的数据
          wx.removeStorageSync('sellEvaluationData');
        },
        fail: (err) => {
          console.error('跳转失败:', err);
          wx.showToast({
            title: '提交成功',
            icon: 'success'
          });
        }
      });
    }, 1500);
  },

  /**
   * 买房版表单验证
   */
  validateBuyForm: function() {
    const { buyFormData } = this.data;
    
    if (!buyFormData.communityName.trim()) {
      wx.showToast({ title: '请输入小区名称', icon: 'none' });
      return false;
    }
    
    if (!buyFormData.propertyType) {
      wx.showToast({ title: '请选择房产类型', icon: 'none' });
      return false;
    }
    
    if (!buyFormData.buildingArea.trim()) {
      wx.showToast({ title: '请输入建筑面积', icon: 'none' });
      return false;
    }
    
    return true;
  },

  /**
   * 卖房版表单验证
   */
  validateSellForm: function() {
    const { sellFormData } = this.data;
    
    if (!sellFormData.communityName.trim()) {
      wx.showToast({ title: '请输入小区名称', icon: 'none' });
      return false;
    }
    
    if (!sellFormData.propertyType) {
      wx.showToast({ title: '请选择房产类型', icon: 'none' });
      return false;
    }
    
    if (!sellFormData.buildingArea.trim()) {
      wx.showToast({ title: '请输入建筑面积', icon: 'none' });
      return false;
    }
    
    return true;
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const title = this.data.currentTab === 0 ?
      '房子应不应该买 - 买房前专业评测分析' :
      '房子应不应该卖 - 卖房时机专业评测';

    return {
      title: title,
      desc: '专业评估，精准分析',
      path: '/pages/property-evaluation/property-evaluation'
    };
  }
});
